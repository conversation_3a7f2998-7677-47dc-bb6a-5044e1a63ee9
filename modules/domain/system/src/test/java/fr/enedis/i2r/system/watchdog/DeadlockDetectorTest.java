package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mockito;

class DeadlockDetectorTest {

    private DeadlockDetector deadlockDetector;

    @BeforeEach
    void setUp() {
        // Create a test instance that won't call System.exit()
        deadlockDetector = new DeadlockDetector(
            ManagementFactory.getThreadMXBean(),
            () -> { /* No-op for testing */ }
        );
    }

    @Test
    void aucun_deadlock_detecte() {
        assertFalse(deadlockDetector.hasDeadlocks());
        assertFalse(deadlockDetector.checkAndHandleDeadlocks());
        assertEquals("No deadlocks detected", deadlockDetector.getDeadlockStatus());
    }

    @Test
    void format_statut_deadlock_est_correct() {
        String status = deadlockDetector.getDeadlockStatus();
        assertNotNull(status);
        assertFalse(status.isEmpty());

        assertTrue(status.equals("No deadlocks detected") || status.startsWith("DEADLOCK:"));
    }

    @Test
    void situation_de_deadlock_est_detectee() {
        // Create a mock ThreadMXBean that simulates a deadlock
        ThreadMXBean mockThreadBean = Mockito.mock(ThreadMXBean.class);
        ThreadInfo mockThreadInfo1 = Mockito.mock(ThreadInfo.class);
        ThreadInfo mockThreadInfo2 = Mockito.mock(ThreadInfo.class);

        // Configure mock to return deadlocked threads
        long[] deadlockedThreadIds = {1L, 2L};
        Mockito.when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);
        Mockito.when(mockThreadBean.getThreadInfo(deadlockedThreadIds)).thenReturn(new ThreadInfo[]{mockThreadInfo1, mockThreadInfo2});

        // Configure mock thread info
        Mockito.when(mockThreadInfo1.getThreadName()).thenReturn("DeadlockTest-Thread1");
        Mockito.when(mockThreadInfo1.getThreadId()).thenReturn(1L);
        Mockito.when(mockThreadInfo1.getThreadState()).thenReturn(Thread.State.BLOCKED);
        Mockito.when(mockThreadInfo1.getLockName()).thenReturn("java.lang.Object@12345");

        Mockito.when(mockThreadInfo2.getThreadName()).thenReturn("DeadlockTest-Thread2");
        Mockito.when(mockThreadInfo2.getThreadId()).thenReturn(2L);
        Mockito.when(mockThreadInfo2.getThreadState()).thenReturn(Thread.State.BLOCKED);
        Mockito.when(mockThreadInfo2.getLockName()).thenReturn("java.lang.Object@67890");

        // Create detector with mock
        DeadlockDetector mockDeadlockDetector = new DeadlockDetector(mockThreadBean, () -> { /* No-op for testing */ });

        // Check for deadlocks
        assertTrue(mockDeadlockDetector.hasDeadlocks(), "Deadlock should be detected");
        assertTrue(mockDeadlockDetector.checkAndHandleDeadlocks(), "checkAndHandleDeadlocks should return true");

        String status = mockDeadlockDetector.getDeadlockStatus();
        assertTrue(status.contains("DEADLOCK"), "Status should indicate deadlock: " + status);
        assertTrue(status.contains("threads deadlocked"), "Status should mention deadlocked threads: " + status);
    }

    @Test
    void hasDeadlocks_retourne_false_quand_aucun_deadlock() {
        // Test multiple calls to ensure consistency
        assertFalse(deadlockDetector.hasDeadlocks());
        assertFalse(deadlockDetector.hasDeadlocks());
        assertFalse(deadlockDetector.hasDeadlocks());
    }

    @Test
    void getDeadlockStatus_format_correct_sans_deadlock() {
        String status = deadlockDetector.getDeadlockStatus();
        assertEquals("No deadlocks detected", status);
    }

    @Test
    void getDeadlockStatus_format_correct_avec_deadlock() {
        // Create a mock ThreadMXBean that simulates a deadlock
        ThreadMXBean mockThreadBean = Mockito.mock(ThreadMXBean.class);

        // Configure mock to return deadlocked threads
        long[] deadlockedThreadIds = {3L, 4L};
        Mockito.when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);

        // Create detector with mock
        DeadlockDetector mockDeadlockDetector = new DeadlockDetector(mockThreadBean, () -> { /* No-op for testing */ });

        String status = mockDeadlockDetector.getDeadlockStatus();
        assertTrue(status.startsWith("DEADLOCK:"), "Status should start with DEADLOCK:");
        assertTrue(status.contains("threads deadlocked"), "Status should mention deadlocked threads");
    }

    @Test
    void checkAndHandleDeadlocks_retourne_false_sans_deadlock() {
        // When no deadlocks exist, should return false and not exit
        assertFalse(deadlockDetector.checkAndHandleDeadlocks());
    }

    @Test
    @Timeout(10)
    void multiple_calls_consistent_behavior() {
        // Test that multiple rapid calls don't cause issues
        for (int i = 0; i < 100; i++) {
            assertFalse(deadlockDetector.hasDeadlocks());
            assertEquals("No deadlocks detected", deadlockDetector.getDeadlockStatus());
            assertFalse(deadlockDetector.checkAndHandleDeadlocks());
        }
    }

    @Test
    void threadMXBean_integration_test() {
        // Test that we're using the ThreadMXBean correctly
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        assertNotNull(threadBean, "ThreadMXBean should be available");

        // Test that findDeadlockedThreads works (should return null when no deadlocks)
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        assertTrue(deadlockedThreads == null || deadlockedThreads.length == 0,
                   "Should have no deadlocked threads initially");
    }

    @Test
    void edge_case_empty_thread_info() {
        // This tests the robustness of the code when ThreadInfo might be null
        // (which can happen in some JVM implementations)

        // We can't easily mock this, but we can test that our current implementation
        // handles the normal case correctly
        assertFalse(deadlockDetector.hasDeadlocks());
        String status = deadlockDetector.getDeadlockStatus();
        assertNotNull(status);
        assertFalse(status.isEmpty());
    }

    @Test
    @Timeout(15)
    void deadlock_detection_timing_test() {
        // Test that deadlock detection works within reasonable time bounds using mock
        ThreadMXBean mockThreadBean = Mockito.mock(ThreadMXBean.class);

        // Configure mock to return deadlocked threads
        long[] deadlockedThreadIds = {5L, 6L};
        Mockito.when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);

        // Create detector with mock
        DeadlockDetector mockDeadlockDetector = new DeadlockDetector(mockThreadBean, () -> { /* No-op for testing */ });

        // Test detection timing
        long startTime = System.currentTimeMillis();
        boolean hasDeadlocks = mockDeadlockDetector.hasDeadlocks();
        long detectionTime = System.currentTimeMillis() - startTime;

        assertTrue(hasDeadlocks, "Should detect deadlock");
        assertTrue(detectionTime < 1000, "Detection should be fast (< 1s), was: " + detectionTime + "ms");
    }
}
