package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.Mockito;

/**
 * Integration tests for ThreadWatchdog and DeadlockDetector components
 * working together as they would in a real application.
 */
class WatchdogIntegrationTest {

    private DeadlockDetector deadlockDetector;
    private ThreadWatchdog threadWatchdog;

    @BeforeEach
    void setUp() {
        threadWatchdog = new ThreadWatchdog();
        // Create a test instance that won't call System.exit()
        deadlockDetector = new DeadlockDetector(
            ManagementFactory.getThreadMXBean(),
            () -> { /* No-op for testing */ }
        );
    }

    @AfterEach
    void tearDown() {
        threadWatchdog.clearAll();
        threadWatchdog.shutdown();
    }

    @Test
    @Timeout(10)
    void threadWatchdog_et_deadlockDetector_fonctionnent_ensemble() throws Exception {
        // Test that both components can work together without interference
        String threadName = "integration-test-thread";
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        // Register thread with watchdog
        threadWatchdog.register(threadName);

        // Check that deadlock detector works while watchdog is running
        assertFalse(deadlockDetector.hasDeadlocks());
        assertEquals("No deadlocks detected", deadlockDetector.getDeadlockStatus());
        assertFalse(deadlockDetector.checkAndHandleDeadlocks());

        // Wait for some heartbeats
        Thread.sleep(500);

        // Verify thread is still being monitored
        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();
        assertTrue(threads.containsKey(threadName));

        // Deadlock detection should still work
        assertFalse(deadlockDetector.hasDeadlocks());

        threadWatchdog.unregister(threadName);
    }

    @Test
    @Timeout(15)
    void multiple_threads_avec_deadlock_detection() throws Exception {
        // Test multiple threads being monitored while deadlock detection runs
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        String[] threadNames = {"integration-thread-1", "integration-thread-2", "integration-thread-3"};

        // Register multiple threads
        for (String threadName : threadNames) {
            threadWatchdog.register(threadName);
        }

        // Run deadlock detection multiple times while threads are being monitored
        for (int i = 0; i < 10; i++) {
            assertFalse(deadlockDetector.hasDeadlocks());
            Thread.sleep(100);
        }

        // Verify all threads are still registered
        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();

        for (String threadName : threadNames) {
            assertTrue(threads.containsKey(threadName), "Thread should still be registered: " + threadName);
        }

        // Unregister all threads
        for (String threadName : threadNames) {
            threadWatchdog.unregister(threadName);
        }

        assertEquals(0, threads.size(), "All threads should be unregistered");
    }

    @Test
    @Timeout(20)
    void deadlock_detection_pendant_thread_monitoring() throws Exception {
        // Test deadlock detection while ThreadWatchdog is actively monitoring threads
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        // Register some threads for monitoring
        threadWatchdog.register("monitor-thread-1");
        threadWatchdog.register("monitor-thread-2");

        // Use a mock to simulate deadlock detection without creating real deadlocks
        ThreadMXBean mockThreadBean = Mockito.mock(ThreadMXBean.class);
        long[] deadlockedThreadIds = {7L, 8L};
        Mockito.when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);

        DeadlockDetector mockDeadlockDetector = new DeadlockDetector(mockThreadBean, () -> { /* No-op for testing */ });

        // Deadlock detection should work even while ThreadWatchdog is running
        assertTrue(mockDeadlockDetector.hasDeadlocks(), "Should detect deadlock");
        String status = mockDeadlockDetector.getDeadlockStatus();
        assertTrue(status.contains("DEADLOCK"), "Status should indicate deadlock");

        // ThreadWatchdog should still be functioning
        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();
        assertEquals(2, threads.size(), "Monitored threads should still be registered");

        // Clean up monitored threads
        threadWatchdog.unregister("monitor-thread-1");
        threadWatchdog.unregister("monitor-thread-2");
    }

    @Test
    @Timeout(10)
    void stress_test_concurrent_operations() throws Exception {
        // Stress test with concurrent operations on both components
        threadWatchdog.setHeartbeatInterval(Duration.ofMillis(500));

        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch doneLatch = new CountDownLatch(3);

        // Thread 1: Continuously register/unregister threads
        Thread watchdogThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 50; i++) {
                    String threadName = "stress-thread-" + i;
                    threadWatchdog.register(threadName);
                    Thread.sleep(10);
                    threadWatchdog.unregister(threadName);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        // Thread 2: Continuously check for deadlocks
        Thread deadlockThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 100; i++) {
                    deadlockDetector.hasDeadlocks();
                    deadlockDetector.getDeadlockStatus();
                    Thread.sleep(5);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        // Thread 3: Mixed operations
        Thread mixedThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 30; i++) {
                    threadWatchdog.register("mixed-thread-" + i);
                    deadlockDetector.checkAndHandleDeadlocks();
                    Thread.sleep(15);
                    threadWatchdog.unregister("mixed-thread-" + i);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        watchdogThread.start();
        deadlockThread.start();
        mixedThread.start();

        // Start all operations
        startLatch.countDown();

        // Wait for completion
        assertTrue(doneLatch.await(8, TimeUnit.SECONDS), "All stress test threads should complete");

        // Verify clean state
        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();
        assertEquals(0, threads.size(), "No threads should remain registered after stress test");

        // Verify deadlock detection still works
        assertFalse(deadlockDetector.hasDeadlocks());
    }

    @Test
    @Timeout(10)
    void configuration_changes_pendant_monitoring() throws Exception {
        // Test changing configuration while monitoring is active
        String threadName = "config-change-thread";

        // Start with one interval
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(2));
        threadWatchdog.register(threadName);

        Thread.sleep(1000); // Wait for initial heartbeat

        // Change interval
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        // Register another thread with new interval
        threadWatchdog.register("config-change-thread-2");

        Thread.sleep(1500); // Wait for heartbeats with new interval

        // Verify both threads are still monitored
        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();
        assertEquals(2, threads.size(), "Both threads should be monitored");

        // Deadlock detection should work throughout
        assertFalse(deadlockDetector.hasDeadlocks());

        threadWatchdog.unregister(threadName);
        threadWatchdog.unregister("config-change-thread-2");
    }
}
