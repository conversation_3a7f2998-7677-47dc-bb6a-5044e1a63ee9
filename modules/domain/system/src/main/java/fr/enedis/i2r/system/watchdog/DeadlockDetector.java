package fr.enedis.i2r.system.watchdog;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@FunctionalInterface
interface DeadlockHandler {
    void triggerRestart();
}

public class DeadlockDetector {

    private final Logger logger = LoggerFactory.getLogger(DeadlockDetector.class);
    private final ThreadMXBean threadBean;
    private final DeadlockHandler deadlockHandler;

    public DeadlockDetector() {
        this(ManagementFactory.getThreadMXBean(), createDefaultDeadlockHandler());
    }

    public DeadlockDetector(ThreadMXBean threadBean, DeadlockHandler deadlockHandler) {
        this.threadBean = threadBean;
        this.deadlockHandler = deadlockHandler;
    }

    private static DeadlockHandler createDefaultDeadlockHandler() {
        return () -> {
            Logger logger = LoggerFactory.getLogger(DeadlockDetector.class);
            // Give time for logs to flush
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            // Exit with error code - systemd will restart
            logger.error("CRITICAL: Triggering System.exit(1) due to deadlock");
            System.exit(1);
        };
    }

    public boolean hasDeadlocks() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        return deadlockedThreads != null && deadlockedThreads.length > 0;
    }

    public boolean checkAndHandleDeadlocks() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();

        if (deadlockedThreads != null && deadlockedThreads.length > 0) {
            logger.error("DEADLOCK DETECTED! {} threads involved: {}",
                deadlockedThreads.length, Arrays.toString(deadlockedThreads));

            var threadInfos = threadBean.getThreadInfo(deadlockedThreads);
            for (var threadInfo : threadInfos) {
                if (threadInfo != null) {
                    logger.error("Deadlocked thread: {} (ID: {}) - State: {} - Blocked on: {}",
                        threadInfo.getThreadName(),
                        threadInfo.getThreadId(),
                        threadInfo.getThreadState(),
                        threadInfo.getLockName());
                }
            }

            logger.error("CRITICAL: Initiating restart due to deadlock");
            deadlockHandler.triggerRestart();

            return true;
        }
        return false;
    }

}
